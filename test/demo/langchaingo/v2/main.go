package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
)

// ConversationConfig 对话配置
type ConversationConfig struct {
	MaxHistoryRounds  int     // 保留的最大历史轮数
	MaxTokensPerCall  int     // 每次调用最大token数
	Temperature       float64 // 温度参数
	Model             string  // 使用的模型
	SystemPrompt      string  // 系统提示词
	SummaryThreshold  int     // 触发摘要的消息数量阈值
	EnableAutoSummary bool    // 是否启用自动摘要
}

// DefaultConfig 默认配置
func DefaultConfig() ConversationConfig {
	return ConversationConfig{
		MaxHistoryRounds:  10,
		MaxTokensPerCall:  4000,
		Temperature:       0.7,
		Model:             "gpt-3.5-turbo",
		SystemPrompt:      "你是一个有用的AI助手。",
		SummaryThreshold:  20,
		EnableAutoSummary: true,
	}
}

// Message 消息结构
type Message struct {
	Role      string    `json:"role"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
}

// ConversationMemory 对话记忆管理
type ConversationMemory struct {
	messages        []Message
	summary         string
	config          ConversationConfig
	llm             llms.Model
	totalTokensUsed int
}

// NewConversationMemory 创建新的对话记忆实例
func NewConversationMemory(llm llms.Model, config ConversationConfig) *ConversationMemory {
	return &ConversationMemory{
		messages: make([]Message, 0),
		llm:      llm,
		config:   config,
	}
}

// AddMessage 添加消息到记忆中
func (cm *ConversationMemory) AddMessage(role, content string) {
	message := Message{
		Role:      role,
		Content:   content,
		Timestamp: time.Now(),
	}
	cm.messages = append(cm.messages, message)

	// 检查是否需要自动摘要
	if cm.config.EnableAutoSummary && len(cm.messages) > cm.config.SummaryThreshold {
		if err := cm.compressHistory(context.Background()); err != nil {
			log.Printf("自动摘要失败: %v", err)
		}
	}
}

// compressHistory 压缩历史记录
func (cm *ConversationMemory) compressHistory(ctx context.Context) error {
	if len(cm.messages) <= cm.config.MaxHistoryRounds*2 {
		return nil
	}

	// 确定需要摘要的消息范围
	keepRecent := cm.config.MaxHistoryRounds * 2 // 保留最近N轮对话
	if len(cm.messages) <= keepRecent {
		return nil
	}

	// 获取需要摘要的历史消息
	toSummarize := cm.messages[:len(cm.messages)-keepRecent]

	// 构建摘要提示
	var summaryBuilder strings.Builder
	summaryBuilder.WriteString("请简洁地总结以下对话的关键信息和上下文：\n\n")

	for _, msg := range toSummarize {
		summaryBuilder.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
	}

	// 生成摘要
	summaryPrompt := []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, summaryBuilder.String()),
	}

	response, err := cm.llm.GenerateContent(ctx, summaryPrompt, llms.WithMaxTokens(500))
	if err != nil {
		return fmt.Errorf("生成摘要失败: %w", err)
	}

	// 更新摘要和消息历史
	if len(response.Choices) > 0 {
		newSummary := response.Choices[0].Content
		if cm.summary != "" {
			cm.summary = cm.summary + "\n\n--- 新摘要 ---\n" + newSummary
		} else {
			cm.summary = newSummary
		}

		// 只保留最近的消息
		cm.messages = cm.messages[len(cm.messages)-keepRecent:]

		log.Printf("历史记录已压缩，保留最近 %d 轮对话", cm.config.MaxHistoryRounds)
	}

	return nil
}

// GetContextMessages 获取用于API调用的上下文消息
func (cm *ConversationMemory) GetContextMessages() []llms.MessageContent {
	var contextMessages []llms.MessageContent

	// 添加系统提示
	systemContent := cm.config.SystemPrompt
	if cm.summary != "" {
		systemContent += "\n\n历史对话摘要:\n" + cm.summary
	}
	contextMessages = append(contextMessages, llms.TextParts(llms.ChatMessageTypeSystem, systemContent))

	// 添加历史消息
	for _, msg := range cm.messages {
		var msgType llms.ChatMessageType
		switch msg.Role {
		case "user":
			msgType = llms.ChatMessageTypeHuman
		case "assistant":
			msgType = llms.ChatMessageTypeAI
		case "system":
			msgType = llms.ChatMessageTypeSystem
		default:
			msgType = llms.ChatMessageTypeHuman
		}
		contextMessages = append(contextMessages, llms.TextParts(msgType, msg.Content))
	}

	return contextMessages
}

// GetStats 获取对话统计信息
func (cm *ConversationMemory) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_messages":    len(cm.messages),
		"has_summary":       cm.summary != "",
		"summary_length":    len(cm.summary),
		"total_tokens_used": cm.totalTokensUsed,
	}
}

// ClearHistory 清空对话历史
func (cm *ConversationMemory) ClearHistory() {
	cm.messages = make([]Message, 0)
	cm.summary = ""
	log.Println("对话历史已清空")
}

// ConversationManager 对话管理器
type ConversationManager struct {
	llm    llms.Model
	memory *ConversationMemory
	config ConversationConfig
}

// NewConversationManager 创建对话管理器
func NewConversationManager(baseUrl, model, apiKey string, config ConversationConfig) (*ConversationManager, error) {
	// 创建LLM实例（这里以OpenAI为例）
	llm, err := openai.New(
		openai.WithToken(apiKey),
		openai.WithModel(model), // 可以更换为其他模型
		openai.WithBaseURL(baseUrl),
	)
	if err != nil {
		return nil, fmt.Errorf("创建LLM实例失败: %w", err)
	}

	memory := NewConversationMemory(llm, config)

	return &ConversationManager{
		llm:    llm,
		memory: memory,
		config: config,
	}, nil
}

// Chat 基础对话调用
func (cm *ConversationManager) Chat(ctx context.Context, userInput string) (string, error) {
	// 添加用户消息到记忆
	cm.memory.AddMessage("user", userInput)

	// 获取上下文消息
	messages := cm.memory.GetContextMessages()

	// 调用LLM
	response, err := cm.llm.GenerateContent(ctx, messages,
		llms.WithMaxTokens(cm.config.MaxTokensPerCall),
		llms.WithTemperature(cm.config.Temperature),
	)
	if err != nil {
		return "", fmt.Errorf("LLM调用失败: %w", err)
	}

	choices := response.Choices
	if len(choices) == 0 {
		return "", fmt.Errorf("未收到有效响应")
	}

	assistantReply := choices[0].Content

	// 添加助手回复到记忆
	cm.memory.AddMessage("assistant", assistantReply)

	// 更新token使用统计
	cm.UpdateTotalTokensUsed(choices)

	return assistantReply, nil
}

// StreamChat 流式对话调用
func (cm *ConversationManager) StreamChat(ctx context.Context, userInput string, callback func(string)) error {
	// 添加用户消息到记忆
	cm.memory.AddMessage("user", userInput)

	// 获取上下文消息
	messages := cm.memory.GetContextMessages()

	// 用于收集完整回复的缓冲区
	var fullResponse strings.Builder

	// 创建流式调用
	response, err := cm.llm.GenerateContent(ctx, messages,
		llms.WithMaxTokens(cm.config.MaxTokensPerCall),
		llms.WithTemperature(cm.config.Temperature),
		llms.WithStreamingFunc(func(ctx context.Context, chunk []byte) error {
			chunkStr := string(chunk)
			fullResponse.WriteString(chunkStr)

			// 调用回调函数，实时返回数据块
			if callback != nil {
				callback(chunkStr)
			}

			return nil
		}),
	)

	if err != nil {
		return fmt.Errorf("流式调用失败: %w", err)
	}

	choices := response.Choices
	if len(choices) == 0 {
		return fmt.Errorf("未收到有效响应")
	}

	// 添加完整的助手回复到记忆
	assistantReply := fullResponse.String()
	if assistantReply != "" {
		cm.memory.AddMessage("assistant", assistantReply)
	}

	// 更新token使用统计
	cm.UpdateTotalTokensUsed(choices)

	return nil
}

func (cm *ConversationManager) UpdateTotalTokensUsed(choices []*llms.ContentChoice) {
	if totalTokens, ok := choices[0].GenerationInfo["TotalTokens"]; ok {
		cm.memory.totalTokensUsed += totalTokens.(int)
	}
}

// GetMemoryStats 获取记忆统计信息
func (cm *ConversationManager) GetMemoryStats() map[string]interface{} {
	return cm.memory.GetStats()
}

// ClearMemory 清空记忆
func (cm *ConversationManager) ClearMemory() {
	cm.memory.ClearHistory()
}

// ExportConversation 导出对话历史
func (cm *ConversationManager) ExportConversation() []Message {
	return append([]Message(nil), cm.memory.messages...)
}

// ImportConversation 导入对话历史
func (cm *ConversationManager) ImportConversation(messages []Message) {
	cm.memory.messages = append([]Message(nil), messages...)
}

// 使用示例
func main() {
	// 配置
	config := DefaultConfig()
	config.SystemPrompt = "你是一个专业的技术助手，擅长解答编程和技术问题。"
	config.MaxHistoryRounds = 10

	baseUrl := "https://tbai.xin/v1"
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		fmt.Println("请设置环境变量 OPENAI_API_KEY 后重试")
		return
	}

	model := map[int]string{
		1:  "deepseek-v3.1",
		2:  "deepseek-r1",
		3:  "deepseek-v3",
		4:  "gemini-2.5-flash-lite",
		5:  "gemini-2.5-flash-nothinking",
		6:  "gemini-2.5-pro",
		7:  "gemini-2.5-flash",
		8:  "gemini-2.5-flash-search",
		9:  "gemini-2.5-pro-search",
		10: "gpt-4.1-nano",
		11: "gpt-4o-mini",
		12: "gpt-4.1-mini",
		13: "kimi-k2",
		14: "Kimi-K2-Instruct",
		15: "Qwen3-235B-A22B-Thinking-2507",
		16: "Qwen3-Coder-480B-A35B-Instruct",
		17: "qwen3-coder",
		18: "Qwen3-235B-A22B-Instruct-2507",
		19: "GLM-4.5",
		20: "GLM-4.1V-9B-Thinking",
		21: "openrouter:moonshotai/kimi-k2:free",
		22: "gpt-5",
	}

	// 创建对话管理器
	manager, err := NewConversationManager(baseUrl, model[12], apiKey, config)
	if err != nil {
		log.Fatalf("创建对话管理器失败: %v", err)
	}

	ctx := context.Background()

	// 示例1: 基础对话
	fmt.Println("=== 基础对话示例 ===")
	response1, err := manager.Chat(ctx, "什么是Go语言？")
	if err != nil {
		log.Printf("对话失败: %v", err)
	} else {
		fmt.Printf("用户: 什么是Go语言？\n")
		fmt.Printf("助手: %s\n\n", response1)
	}

	// 示例2: 继续对话（测试记忆功能）
	response2, err := manager.Chat(ctx, "它有什么优势？")
	if err != nil {
		log.Printf("对话失败: %v", err)
	} else {
		fmt.Printf("用户: 它有什么优势？\n")
		fmt.Printf("助手: %s\n\n", response2)
	}

	// 示例3: 流式对话
	fmt.Println("=== 流式对话示例 ===")
	fmt.Printf("用户: 请详细解释Go的并发模型\n")
	fmt.Printf("助手: ")

	err = manager.StreamChat(ctx, "请详细解释Go的并发模型", func(chunk string) {
		fmt.Print(chunk) // 实时输出数据块
	})
	if err != nil {
		log.Printf("流式对话失败: %v", err)
	}
	fmt.Println()

	// 打印统计信息
	fmt.Println("\n=== 记忆统计 ===")
	stats := manager.GetMemoryStats()
	for key, value := range stats {
		fmt.Printf("%s: %v\n", key, value)
	}
}
