package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
)

// 配置结构
type Config struct {
	BaseUrl        string
	OpenAIToken    string
	Model          string
	RedisAddr      string
	RedisPassword  string
	ServerPort     string
	MaxMemorySize  int
	SessionTimeout time.Duration
}

// 会话管理器
type SessionManager struct {
	sessions    sync.Map
	llm         llms.Model
	redisClient *redis.Client
	config      *Config
}

// 会话信息
type SessionInfo struct {
	ID           string                 `json:"id"`
	Memory       *RedisMemory           `json:"-"`
	Chain        *chains.LLMChain       `json:"-"`
	LastAccess   time.Time              `json:"last_access"`
	MessageCount int                    `json:"message_count"`
	CreatedAt    time.Time              `json:"created_at"`
	UserID       string                 `json:"user_id,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// 自定义Redis记忆实现
type RedisMemory struct {
	client     *redis.Client
	sessionID  string
	maxLength  int
	expiration time.Duration
	mu         sync.RWMutex
}

func NewRedisMemory(client *redis.Client, sessionID string, maxLength int, expiration time.Duration) *RedisMemory {
	return &RedisMemory{
		client:     client,
		sessionID:  sessionID,
		maxLength:  maxLength,
		expiration: expiration,
	}
}

func (r *RedisMemory) MemoryVariables(ctx context.Context) []string {
	return []string{"history"}
}

func (r *RedisMemory) LoadMemoryVariables(ctx context.Context, inputs map[string]any) (map[string]any, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	key := fmt.Sprintf("chat_history:%s", r.sessionID)

	messages, err := r.client.LRange(ctx, key, 0, -1).Result()
	if err != nil {
		if err == redis.Nil {
			return map[string]any{"history": ""}, nil
		}
		return nil, fmt.Errorf("failed to load memory: %w", err)
	}

	var history string
	for _, msgStr := range messages {
		var msg map[string]interface{}
		if err := json.Unmarshal([]byte(msgStr), &msg); err != nil {
			continue
		}

		role := msg["role"].(string)
		content := msg["content"].(string)
		timestamp := msg["timestamp"].(string)

		history += fmt.Sprintf("[%s] %s: %s\n", timestamp, role, content)
	}

	return map[string]any{"history": history}, nil
}

func (r *RedisMemory) GetMemoryKey(ctx context.Context) string {
	return fmt.Sprintf("chat_history:%s", r.sessionID)
}

func (r *RedisMemory) SaveContext(ctx context.Context, inputs, outputs map[string]any) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	key := fmt.Sprintf("chat_history:%s", r.sessionID)
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	// 保存用户输入
	if input, ok := inputs["input"]; ok {
		userMsg := map[string]interface{}{
			"role":      "human",
			"content":   input,
			"timestamp": timestamp,
		}
		msgData, _ := json.Marshal(userMsg)
		if err := r.client.RPush(ctx, key, string(msgData)).Err(); err != nil {
			return fmt.Errorf("failed to save user message: %w", err)
		}
	}

	// 保存AI回复
	if output, ok := outputs["text"]; ok {
		aiMsg := map[string]interface{}{
			"role":      "assistant",
			"content":   output,
			"timestamp": timestamp,
		}
		msgData, _ := json.Marshal(aiMsg)
		if err := r.client.RPush(ctx, key, string(msgData)).Err(); err != nil {
			return fmt.Errorf("failed to save AI message: %w", err)
		}
	}

	// 限制历史记录长度
	if err := r.client.LTrim(ctx, key, -int64(r.maxLength), -1).Err(); err != nil {
		return fmt.Errorf("failed to trim history: %w", err)
	}

	// 设置过期时间
	if err := r.client.Expire(ctx, key, r.expiration).Err(); err != nil {
		return fmt.Errorf("failed to set expiration: %w", err)
	}

	return nil
}

func (r *RedisMemory) Clear(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	key := fmt.Sprintf("chat_history:%s", r.sessionID)
	return r.client.Del(ctx, key).Err()
}

// 加载配置
func LoadConfig() *Config {
	return &Config{
		Model:          "gpt-4o-mini",
		BaseUrl:        "https://tbai.xin/v1",
		OpenAIToken:    getEnv("OPENAI_API_KEY", ""),
		RedisAddr:      getEnv("REDIS_ADDR", "localhost:6379"),
		RedisPassword:  getEnv("REDIS_PASSWORD", ""),
		ServerPort:     getEnv("SERVER_PORT", "8080"),
		MaxMemorySize:  getEnvInt("MAX_MEMORY_SIZE", 50),
		SessionTimeout: time.Duration(getEnvInt("SESSION_TIMEOUT_HOURS", 24)) * time.Hour,
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// 初始化会话管理器
func NewSessionManager(config *Config) (*SessionManager, error) {
	// 初始化OpenAI客户端
	llm, err := openai.New(
		openai.WithToken(config.OpenAIToken),
		openai.WithModel(config.Model), // 可以更换为其他模型
		openai.WithBaseURL(config.BaseUrl),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize OpenAI client: %w", err)
	}

	// 初始化Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr,
		Password: config.RedisPassword,
		DB:       0,
	})

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := redisClient.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	sm := &SessionManager{
		llm:         llm,
		redisClient: redisClient,
		config:      config,
	}

	// 启动清理任务
	go sm.startCleanupTask()

	return sm, nil
}

// 创建新会话
func (sm *SessionManager) CreateSession(userID string, metadata map[string]interface{}) (*SessionInfo, error) {
	sessionID := uuid.New().String()

	// 创建Redis内存存储
	mem := NewRedisMemory(sm.redisClient, sessionID, sm.config.MaxMemorySize, sm.config.SessionTimeout)

	// 创建对话链
	chain := chains.NewConversation(sm.llm, mem)

	session := &SessionInfo{
		ID:           sessionID,
		Memory:       mem,
		Chain:        &chain,
		LastAccess:   time.Now(),
		MessageCount: 0,
		CreatedAt:    time.Now(),
		UserID:       userID,
		Metadata:     metadata,
	}

	sm.sessions.Store(sessionID, session)

	log.Printf("Created new session: %s for user: %s", sessionID, userID)
	return session, nil
}

// 获取会话
func (sm *SessionManager) GetSession(sessionID string) (*SessionInfo, bool) {
	if value, ok := sm.sessions.Load(sessionID); ok {
		session := value.(*SessionInfo)
		session.LastAccess = time.Now()
		return session, true
	}

	// 尝试从Redis恢复会话
	return sm.restoreSessionFromRedis(sessionID)
}

// 从Redis恢复会话
func (sm *SessionManager) restoreSessionFromRedis(sessionID string) (*SessionInfo, bool) {
	ctx := context.Background()
	key := fmt.Sprintf("session_info:%s", sessionID)

	sessionData, err := sm.redisClient.Get(ctx, key).Result()
	if err != nil {
		return nil, false
	}

	var sessionInfo SessionInfo
	if err := json.Unmarshal([]byte(sessionData), &sessionInfo); err != nil {
		return nil, false
	}

	// 重新创建内存和链
	mem := NewRedisMemory(sm.redisClient, sessionID, sm.config.MaxMemorySize, sm.config.SessionTimeout)
	chain := chains.NewConversation(sm.llm, mem)

	sessionInfo.Memory = mem
	sessionInfo.Chain = &chain
	sessionInfo.LastAccess = time.Now()

	sm.sessions.Store(sessionID, &sessionInfo)
	return &sessionInfo, true
}

// 处理消息
func (sm *SessionManager) ProcessMessage(sessionID, message string) (string, error) {
	session, exists := sm.GetSession(sessionID)
	if !exists {
		return "", fmt.Errorf("session not found: %s", sessionID)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用对话链
	response, err := session.Chain.Call(ctx, map[string]any{
		"input": message,
	})
	if err != nil {
		return "", fmt.Errorf("failed to process message: %w", err)
	}

	// 更新会话信息
	session.MessageCount++
	session.LastAccess = time.Now()

	// 保存会话信息到Redis
	sm.saveSessionToRedis(session)

	responseText, ok := response["text"].(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	return responseText, nil
}

// 保存会话到Redis
func (sm *SessionManager) saveSessionToRedis(session *SessionInfo) {
	ctx := context.Background()
	key := fmt.Sprintf("session_info:%s", session.ID)

	sessionData, _ := json.Marshal(session)
	sm.redisClient.Set(ctx, key, sessionData, sm.config.SessionTimeout)
}

// 删除会话
func (sm *SessionManager) DeleteSession(sessionID string) error {
	if session, ok := sm.GetSession(sessionID); ok {
		// 清理内存
		if err := session.Memory.Clear(context.Background()); err != nil {
			log.Printf("Failed to clear memory for session %s: %v", sessionID, err)
		}

		// 删除会话信息
		ctx := context.Background()
		key := fmt.Sprintf("session_info:%s", sessionID)
		sm.redisClient.Del(ctx, key)

		sm.sessions.Delete(sessionID)
		log.Printf("Deleted session: %s", sessionID)
	}
	return nil
}

// 清理任务
func (sm *SessionManager) startCleanupTask() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		sm.cleanupExpiredSessions()
	}
}

func (sm *SessionManager) cleanupExpiredSessions() {
	now := time.Now()
	var expiredSessions []string

	sm.sessions.Range(func(key, value interface{}) bool {
		sessionID := key.(string)
		session := value.(*SessionInfo)

		if now.Sub(session.LastAccess) > sm.config.SessionTimeout {
			expiredSessions = append(expiredSessions, sessionID)
		}
		return true
	})

	for _, sessionID := range expiredSessions {
		sm.DeleteSession(sessionID)
	}

	if len(expiredSessions) > 0 {
		log.Printf("Cleaned up %d expired sessions", len(expiredSessions))
	}
}

// HTTP请求结构
type ChatRequest struct {
	SessionID string                 `json:"session_id,omitempty"`
	Message   string                 `json:"message" binding:"required"`
	UserID    string                 `json:"user_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type ChatResponse struct {
	SessionID string                 `json:"session_id"`
	Response  string                 `json:"response"`
	MessageID string                 `json:"message_id"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// HTTP处理器
type ChatHandler struct {
	sessionManager *SessionManager
}

func NewChatHandler(sm *SessionManager) *ChatHandler {
	return &ChatHandler{sessionManager: sm}
}

// 处理聊天请求
func (h *ChatHandler) HandleChat(c *gin.Context) {
	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	var session *SessionInfo
	var err error

	// 获取或创建会话
	if req.SessionID != "" {
		if s, exists := h.sessionManager.GetSession(req.SessionID); exists {
			session = s
		} else {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "session_not_found",
				Code:    404,
				Message: "Session not found",
			})
			return
		}
	} else {
		session, err = h.sessionManager.CreateSession(req.UserID, req.Metadata)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "session_creation_failed",
				Code:    500,
				Message: err.Error(),
			})
			return
		}
	}

	// 处理消息
	response, err := h.sessionManager.ProcessMessage(session.ID, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "processing_failed",
			Code:    500,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ChatResponse{
		SessionID: session.ID,
		Response:  response,
		MessageID: uuid.New().String(),
		Timestamp: time.Now(),
		Metadata:  session.Metadata,
	})
}

// 获取会话信息
func (h *ChatHandler) GetSession(c *gin.Context) {
	sessionID := c.Param("session_id")

	session, exists := h.sessionManager.GetSession(sessionID)
	if !exists {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "session_not_found",
			Code:    404,
			Message: "Session not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"session_id":    session.ID,
		"user_id":       session.UserID,
		"message_count": session.MessageCount,
		"created_at":    session.CreatedAt,
		"last_access":   session.LastAccess,
		"metadata":      session.Metadata,
	})
}

// 删除会话
func (h *ChatHandler) DeleteSession(c *gin.Context) {
	sessionID := c.Param("session_id")

	if err := h.sessionManager.DeleteSession(sessionID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "deletion_failed",
			Code:    500,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Session deleted successfully",
	})
}

// 健康检查
func (h *ChatHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
	})
}

// 中间件：请求日志
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)

		log.Printf("%s %s %d %v %s",
			c.Request.Method,
			c.Request.RequestURI,
			c.Writer.Status(),
			duration,
			c.ClientIP(),
		)
	}
}

// 中间件：错误处理
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("Panic recovered: %v", err)
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "internal_error",
					Code:    500,
					Message: "Internal server error",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// 主函数
func main() {
	// 加载配置
	config := LoadConfig()

	if config.OpenAIToken == "" {
		log.Fatal("OPENAI_API_KEY environment variable is required")
	}

	// 初始化会话管理器
	sessionManager, err := NewSessionManager(config)
	if err != nil {
		log.Fatalf("Failed to initialize session manager: %v", err)
	}

	// 初始化HTTP处理器
	handler := NewChatHandler(sessionManager)

	// 设置Gin路由
	gin.SetMode(gin.ReleaseMode)
	r := gin.New()

	// 添加中间件
	r.Use(RequestLogger())
	r.Use(ErrorHandler())
	r.Use(gin.Recovery())

	// API路由
	api := r.Group("/api/v1")
	{
		api.POST("/chat", handler.HandleChat)
		api.GET("/sessions/:session_id", handler.GetSession)
		api.DELETE("/sessions/:session_id", handler.DeleteSession)
		api.GET("/health", handler.HealthCheck)
	}

	// 启动服务器
	log.Printf("Starting server on port %s", config.ServerPort)
	log.Printf("API endpoints:")
	log.Printf("  POST   /api/v1/chat")
	log.Printf("  GET    /api/v1/sessions/:session_id")
	log.Printf("  DELETE /api/v1/sessions/:session_id")
	log.Printf("  GET    /api/v1/health")

	if err := r.Run(":" + config.ServerPort); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
