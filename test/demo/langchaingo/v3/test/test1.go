// test_client.go - 测试客户端
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试客户端结构
type TestClient struct {
	baseURL string
	client  *http.Client
}

// 聊天请求结构
type TestChatRequest struct {
	SessionID string                 `json:"session_id,omitempty"`
	Message   string                 `json:"message"`
	UserID    string                 `json:"user_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// 聊天响应结构
type TestChatResponse struct {
	SessionID string                 `json:"session_id"`
	Response  string                 `json:"response"`
	MessageID string                 `json:"message_id"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// 会话信息结构
type SessionInfoT struct {
	SessionID    string                 `json:"session_id"`
	UserID       string                 `json:"user_id"`
	MessageCount int                    `json:"message_count"`
	CreatedAt    time.Time              `json:"created_at"`
	LastAccess   time.Time              `json:"last_access"`
	Metadata     map[string]interface{} `json:"metadata"`
}

func NewTestClient(baseURL string) *TestClient {
	return &TestClient{
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// 发送聊天消息
func (c *TestClient) SendMessage(req *TestChatRequest) (*TestChatResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.client.Post(
		c.baseURL+"/api/v1/chat",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var chatResp TestChatResponse
	if err := json.Unmarshal(body, &chatResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &chatResp, nil
}

// 获取会话信息
func (c *TestClient) GetSession(sessionID string) (*SessionInfoT, error) {
	resp, err := c.client.Get(c.baseURL + "/api/v1/sessions/" + sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var sessionInfo SessionInfoT
	if err := json.Unmarshal(body, &sessionInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &sessionInfo, nil
}

// 删除会话
func (c *TestClient) DeleteSession(sessionID string) error {
	req, err := http.NewRequest("DELETE", c.baseURL+"/api/v1/sessions/"+sessionID, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("delete failed with status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// 健康检查
func (c *TestClient) HealthCheck() error {
	resp, err := c.client.Get(c.baseURL + "/api/v1/health")
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}

	return nil
}

// 测试多轮对话
func testMultiTurnConversation(client *TestClient) error {
	fmt.Println("=== 测试多轮对话 ===")

	// 第一轮对话 - 创建新会话
	resp1, err := client.SendMessage(&TestChatRequest{
		Message: "你好，我是张三，是一名Go开发工程师",
		UserID:  "test_user_001",
		Metadata: map[string]interface{}{
			"source": "test",
			"topic":  "introduction",
		},
	})
	if err != nil {
		return fmt.Errorf("第一轮对话失败: %w", err)
	}

	fmt.Printf("用户: 你好，我是张三，是一名Go开发工程师\n")
	fmt.Printf("AI: %s\n\n", resp1.Response)

	sessionID := resp1.SessionID

	// 第二轮对话 - 测试上下文记忆
	resp2, err := client.SendMessage(&TestChatRequest{
		SessionID: sessionID,
		Message:   "我的名字是什么？",
	})
	if err != nil {
		return fmt.Errorf("第二轮对话失败: %w", err)
	}

	fmt.Printf("用户: 我的名字是什么？\n")
	fmt.Printf("AI: %s\n\n", resp2.Response)

	// 第三轮对话 - 测试职业记忆
	resp3, err := client.SendMessage(&TestChatRequest{
		SessionID: sessionID,
		Message:   "我的职业是什么？",
	})
	if err != nil {
		return fmt.Errorf("第三轮对话失败: %w", err)
	}

	fmt.Printf("用户: 我的职业是什么？\n")
	fmt.Printf("AI: %s\n\n", resp3.Response)

	// 获取会话信息
	sessionInfo, err := client.GetSession(sessionID)
	if err != nil {
		return fmt.Errorf("获取会话信息失败: %w", err)
	}

	fmt.Printf("会话信息:\n")
	fmt.Printf("  会话ID: %s\n", sessionInfo.SessionID)
	fmt.Printf("  用户ID: %s\n", sessionInfo.UserID)
	fmt.Printf("  消息数量: %d\n", sessionInfo.MessageCount)
	fmt.Printf("  创建时间: %s\n", sessionInfo.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  最后访问: %s\n\n", sessionInfo.LastAccess.Format("2006-01-02 15:04:05"))

	// 清理 - 删除会话
	if err := client.DeleteSession(sessionID); err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}

	fmt.Println("✅ 多轮对话测试完成")
	return nil
}

// 测试并发会话
func testConcurrentSessions(client *TestClient) error {
	fmt.Println("=== 测试并发会话 ===")

	// 创建多个并发会话
	sessions := make([]string, 3)

	for i := 0; i < 3; i++ {
		resp, err := client.SendMessage(&TestChatRequest{
			Message: fmt.Sprintf("你好，我是用户%d", i+1),
			UserID:  fmt.Sprintf("test_user_%03d", i+1),
		})
		if err != nil {
			return fmt.Errorf("创建会话%d失败: %w", i+1, err)
		}

		sessions[i] = resp.SessionID
		fmt.Printf("用户%d 会话ID: %s\n", i+1, resp.SessionID)
	}

	// 每个会话都进行对话
	for i, sessionID := range sessions {
		resp, err := client.SendMessage(&TestChatRequest{
			SessionID: sessionID,
			Message:   fmt.Sprintf("我是第%d个用户，请记住我", i+1),
		})
		if err != nil {
			return fmt.Errorf("会话%d对话失败: %w", i+1, err)
		}

		fmt.Printf("用户%d AI回复: %s\n", i+1, resp.Response)
	}

	// 清理所有会话
	for i, sessionID := range sessions {
		if err := client.DeleteSession(sessionID); err != nil {
			fmt.Printf("⚠️  删除会话%d失败: %v\n", i+1, err)
		}
	}

	fmt.Println("✅ 并发会话测试完成")
	return nil
}

// 测试错误处理
func testErrorHandling(client *TestClient) error {
	fmt.Println("=== 测试错误处理 ===")

	// 测试无效会话ID
	_, err := client.SendMessage(&TestChatRequest{
		SessionID: "invalid-session-id",
		Message:   "这应该失败",
	})
	if err == nil {
		return fmt.Errorf("应该返回错误，但没有")
	}
	fmt.Printf("✅ 无效会话ID测试通过: %v\n", err)

	// 测试空消息
	_, err = client.SendMessage(&TestChatRequest{
		Message: "",
	})
	if err == nil {
		return fmt.Errorf("应该返回错误，但没有")
	}
	fmt.Printf("✅ 空消息测试通过: %v\n", err)

	// 测试获取不存在的会话
	_, err = client.GetSession("non-existent-session")
	if err == nil {
		return fmt.Errorf("应该返回错误，但没有")
	}
	fmt.Printf("✅ 不存在会话测试通过: %v\n", err)

	fmt.Println("✅ 错误处理测试完成")
	return nil
}

// 性能测试
func testPerformance(client *TestClient) error {
	fmt.Println("=== 性能测试 ===")

	// 创建会话
	resp, err := client.SendMessage(&TestChatRequest{
		Message: "开始性能测试",
		UserID:  "performance_test_user",
	})
	if err != nil {
		return fmt.Errorf("创建性能测试会话失败: %w", err)
	}

	sessionID := resp.SessionID
	messageCount := 10

	start := time.Now()

	// 发送多条消息测试响应时间
	for i := 0; i < messageCount; i++ {
		_, err := client.SendMessage(&TestChatRequest{
			SessionID: sessionID,
			Message:   fmt.Sprintf("这是第%d条测试消息", i+1),
		})
		if err != nil {
			return fmt.Errorf("发送消息%d失败: %w", i+1, err)
		}
	}

	duration := time.Since(start)
	avgTime := duration / time.Duration(messageCount)

	fmt.Printf("发送%d条消息总时间: %v\n", messageCount, duration)
	fmt.Printf("平均响应时间: %v\n", avgTime)

	// 清理
	client.DeleteSession(sessionID)

	fmt.Println("✅ 性能测试完成")
	return nil
}

func main() {
	// 创建测试客户端
	client := NewTestClient("http://localhost:8080")

	// 健康检查
	fmt.Println("=== 健康检查 ===")
	if err := client.HealthCheck(); err != nil {
		fmt.Printf("❌ 健康检查失败: %v\n", err)
		fmt.Println("请确保服务正在运行 (http://localhost:8080)")
		return
	}
	fmt.Println("✅ 服务健康")

	// 运行所有测试
	tests := []struct {
		name string
		fn   func(*TestClient) error
	}{
		{"多轮对话测试", testMultiTurnConversation},
		{"并发会话测试", testConcurrentSessions},
		{"错误处理测试", testErrorHandling},
		{"性能测试", testPerformance},
	}

	for _, test := range tests {
		fmt.Printf("\n开始运行: %s\n", test.name)
		if err := test.fn(client); err != nil {
			fmt.Printf("❌ %s 失败: %v\n", test.name, err)
		} else {
			fmt.Printf("✅ %s 成功\n", test.name)
		}
		time.Sleep(1 * time.Second) // 测试间隔
	}

	fmt.Println("\n🎉 所有测试完成！")
}

// 使用示例函数
func exampleUsage() {
	client := NewTestClient("http://localhost:8080")

	// 示例1: 简单对话
	resp, err := client.SendMessage(&TestChatRequest{
		Message: "你好，请介绍一下Go语言",
		UserID:  "example_user",
	})
	if err != nil {
		fmt.Printf("发送消息失败: %v\n", err)
		return
	}

	fmt.Printf("AI回复: %s\n", resp.Response)
	sessionID := resp.SessionID

	// 示例2: 继续对话
	resp2, err := client.SendMessage(&TestChatRequest{
		SessionID: sessionID,
		Message:   "Go语言适合做什么项目？",
	})
	if err != nil {
		fmt.Printf("继续对话失败: %v\n", err)
		return
	}

	fmt.Printf("AI回复: %s\n", resp2.Response)

	// 示例3: 获取会话统计
	sessionInfo, err := client.GetSession(sessionID)
	if err != nil {
		fmt.Printf("获取会话信息失败: %v\n", err)
		return
	}

	fmt.Printf("会话消息数: %d\n", sessionInfo.MessageCount)

	// 清理
	client.DeleteSession(sessionID)
}
