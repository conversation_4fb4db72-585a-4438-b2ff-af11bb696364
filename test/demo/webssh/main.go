package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/gorilla/websocket"
	"golang.org/x/crypto/ssh"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool { return true }, // 生产环境需验证来源
}

func sshHandler(w http.ResponseWriter, r *http.Request) {
	// 从查询参数获取 SSH 连接信息
	host := r.URL.Query().Get("host")
	if host == "" {
		http.Error(w, "Missing host parameter", http.StatusBadRequest)
		return
	}
	username := r.URL.Query().Get("username")
	if username == "" {
		http.Error(w, "Missing username parameter", http.StatusBadRequest)
		return
	}
	password := r.URL.Query().Get("password")
	if password == "" {
		http.Error(w, "Missing password parameter", http.StatusBadRequest)
		return
	}

	// 升级 HTTP 为 WebSocket
	ws, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("WebSocket upgrade failed:", err)
		return
	}
	defer ws.Close()

	// 配置 SSH 客户端
	config := &ssh.ClientConfig{
		User: username,
		Auth: []ssh.AuthMethod{
			ssh.Password(password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 生产环境需安全验证
	}
	if !strings.Contains(host, ":") {
		host = host + ":22" // 默认 SSH 端口
	}
	client, err := ssh.Dial("tcp", host, config)
	if err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("SSH connection failed: "+err.Error()))
		return
	}
	defer client.Close()

	// 创建 SSH 会话
	session, err := client.NewSession()
	if err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("Session creation failed: "+err.Error()))
		return
	}
	defer session.Close()

	// 请求 PTY（伪终端）
	modes := ssh.TerminalModes{
		ssh.ECHO:          0,     // 禁用回显（由终端处理）
		ssh.TTY_OP_ISPEED: 14400, // 输入速度
		ssh.TTY_OP_OSPEED: 14400, // 输出速度
	}
	if err := session.RequestPty("xterm", 40, 80, modes); err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("PTY request failed: "+err.Error()))
		return
	}

	// 设置 SSH 会话的输入输出
	stdin, err := session.StdinPipe()
	if err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("Stdin pipe failed: "+err.Error()))
		return
	}
	stdout, err := session.StdoutPipe()
	if err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("Stdout pipe failed: "+err.Error()))
		return
	}
	stderr, err := session.StderrPipe()
	if err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("Stderr pipe failed: "+err.Error()))
		return
	}

	// 启动 Shell
	if err := session.Shell(); err != nil {
		ws.WriteMessage(websocket.TextMessage, []byte("Shell start failed: "+err.Error()))
		return
	}

	// 双向桥接：WebSocket <-> SSH
	go func() {
		_, err := io.Copy(stdin, ws) // WebSocket 输入 -> SSH stdin
		if err != nil {
			log.Println("WebSocket to SSH copy failed:", err)
		}
	}()
	go func() {
		_, err := io.Copy(ws, stdout) // SSH stdout -> WebSocket
		if err != nil {
			log.Println("SSH to WebSocket copy failed:", err)
		}
	}()
	go func() {
		_, err := io.Copy(ws, stderr) // SSH stderr -> WebSocket
		if err != nil {
			log.Println("SSH stderr to WebSocket copy failed:", err)
		}
	}()

	// 等待会话结束
	session.Wait()
}

func main() {
	// 静态文件服务（提供 index.html）
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "index.html")
	})
	// WebSocket 端点
	http.HandleFunc("/ssh", sshHandler)
	fmt.Println("Server running at http://localhost:8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
