package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
)

func main() {
	// 创建 HTTP 客户端
	client := &http.Client{}
	jar, _ := cookiejar.New(nil)
	client.Jar = jar

	// 设置 URL
	apiURL := "https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent"

	// 设置 Cookies
	cookiesStr := `SID=g.a0000wiIw_hFsNG8DKoqz5wxb2nnxk-SEngBqFb1O7IZ0YUpVM_WqMSv4Yod4df4gsOOWfZXqAACgYKAW8SARUSFQHGX2MiyA5F6N-WVJRoX2WuCX6xaBoVAUF8yKqTwnoISekDrYU4QvEKyZIH0076; __Secure-1PSID=g.a0000wiIw_hFsNG8DKoqz5wxb2nnxk-SEngBqFb1O7IZ0YUpVM_WYVykKsIvG_fUMCAndveG-AACgYKAdYSARUSFQHGX2MiKrOdeA4lbegmCecuRsVaTBoVAUF8yKpz7ZjhLNnACgp_MfFKO83X0076; __Secure-3PSID=g.a0000wiIw_hFsNG8DKoqz5wxb2nnxk-SEngBqFb1O7IZ0YUpVM_WqdN_KLbe6U9ueB40053eFgACgYKAT0SARUSFQHGX2Mi0c3nk95iOSXjLErN_Z17RxoVAUF8yKrG3LuKBJh41IpJutJl7eQI0076; HSID=A2r5D3BZRGIl7yjSj; SSID=AI2l3i2JW7LomIEWb; APISID=P9ofWylEMTMYSHOW/AspXvZOmJTFMthTeW; SAPISID=WTxcmPoywWkAoerU/A1-aKcSERURjsgsWO; __Secure-1PAPISID=WTxcmPoywWkAoerU/A1-aKcSERURjsgsWO; __Secure-3PAPISID=WTxcmPoywWkAoerU/A1-aKcSERURjsgsWO; AEC=AVh_V2jrBLhRYa0DhEaiLTEnYMmW8AgS3uA13cVsMgd4twTgdmLs0PQb-A; SEARCH_SAMESITE=CgQI9J4B; S=billing-ui-v3=xKOPeuK8mbxDpcNobR03FLhIxJDeyjIN:billing-ui-v3-efe=xKOPeuK8mbxDpcNobR03FLhIxJDeyjIN; __Secure-1PSIDTS=sidts-CjIB5H03P0VrwivexqTDJxT-fpQednOzjfjdYZQ0jQBPzWzV2lAUeq_Ffr68Ogy32-fYvRAA; __Secure-3PSIDTS=sidts-CjIB5H03P0VrwivexqTDJxT-fpQednOzjfjdYZQ0jQBPzWzV2lAUeq_Ffr68Ogy32-fYvRAA; NID=525=aRDbDufjpXiRiSKJFWSz2MKJD7Z7bdCoz4sb8RLcHzzWQLspG_dfCoDixoWAdTHUJfZniyJ40Z8bgywTySZrwAjqlK2fgLS8_1tw8yeX6tXvQ1Pf-Lv8NUpik6zSw9PjpDNbMsZxfiPGiNJDVFEkoOY_VO8QAGdKoyJS717zJPUn6U-oqAaFniT0POCZrhpd9ctUNSROWS4PzRicTiSaM5kgE9QfBEKBMwdw0nwGooVzF_NXHuCKbPzvHj_26huPcsKm6Bw2BlI77jF7sZUmJhAYoL-4CmjdrPzPUDOh47NOC4-3I6zmrPkYzLfnI8MAcacHguxwZ8tIBvQHYLEzChNc5yGnCADTRTkXl0ObSPAuPe1I9WNT7FLoq11-Y2esW83OUXs3VocowHnrG0tKhGovNrfdgtUbTKNXabHfZmQt8ymrzKRukAAD7ouhuK1wOwigF4Qlt1gXQYg6YgS4_WQbZScINvOb0rgKzrkUXqGXK-T_eRwg9bHBWPIpUvf37OmKob25-r19K_vAOwFsfmNlFggn5J6G7tAHaI5HoDECczjumksTM3xYR66k8f-rL7tVz-Ek3AecSBZKkIsUrxQ3PW0tEkR35BNzMalacS7TvXn3tvfqt7Rx-SlQwtvVJDfj7_rYkvjn3kisvOBQc1RJcimj4tbIZX--emRvCT2JX-beBI7Uu8pdqkgAakfqLwVRiFXxCGe9W04KfMYwc; SIDCC=AKEyXzWwnVbn6ntyWo7SJ6xauuE7C6suLNhA7ajaTJ0LXLcTBKqNmcL4QZ_l5C28M8a4y3LYTw; __Secure-1PSIDCC=AKEyXzUpS3Xqwf1SRNqZw-QK63TOWFPNfwM-4Ds-23wuJxsEdAeHHc6BldBQdgDarlqylwwZAA; __Secure-3PSIDCC=AKEyXzVZPWuY19PqySHuuLhhE9OYoc47IPxpZo4-6BkcOLy1ihh-wiHklBm8pzq1Vy2DHOwO7Os`
	u, _ := url.Parse(apiURL)
	cookies := []*http.Cookie{}
	for _, c := range strings.Split(cookiesStr, "; ") {
		parts := strings.SplitN(c, "=", 2)
		if len(parts) == 2 {
			cookies = append(cookies, &http.Cookie{Name: parts[0], Value: parts[1]})
		}
	}
	jar.SetCookies(u, cookies)

	// 设置请求体 (data-raw)
	data := `["models/gemini-2.5-flash-image-preview",[[[[null,"你是谁"]],"user"]],[[null,null,7,5],[null,null,8,5],[null,null,9,5],[null,null,10,5]],[null,null,null,8192,1,0.95,64,null,null,null,null,null,null,null,[2,1]],"\u0021QUKlQhrNAAakGMIghBhC4-jnkceYmHw7ADQBEArZ1Eo8MK2MCw2JYbAVohYBR36T4-AuNWzkJ2dYgREoEkAbrqePCVcCJHnkwcYxiYNeAgAAABFSAAAAImgBB34AQqP7dk6zS-5LwFHV70n7xoHd34S4lOlGMVNFp289xJuFPichgdzIB1kDK43PaundiYOz3hqE9Ztz-rrL8p665cPho5kDVuTHLb_H5loKGkgat8klMG1ofEMcnUq-4or4UNM_BNrHzh40Vf4hGld6cKhCi2zUi2nLvAdwCJgypWN0N64Z4f8QYYniGktaWt13PRhkkD5kONygP2HLR7c6zUcFyfdhOT_LntvTwT8ymWN5k7p2ZS12Er2GXj6RrbQfYKf12ygcccxBxID1voRQ6AvUJ9C8cG4FEjaa_-U5qFTX9JqSHgB23vT7V6BP8FptANyOdDt5Mrvq5DE2kIeQwGMna71j1_8W_lj_A9hxSJ0qKrhCtHudT-G5U5s0VT3hCKcw67-D0LK9Wh51UM3OOiQjvmf0907xJ9IaQ-m2lLwyWOoL7Sx35guHUhrp7HGfVxz2-HKTFv4UVkI3A3UgWU2idonSEYQh01BBMoemAojTpXSZzFC0afobORECSMKXBRiwGKZi3ENiTyrLTNH81T1fsXY8E1Khh6yLOuAI6Jxo1X6H-Bd9BNs_T48ZbDlPOWI3XybuG2W4E_kqxJWAFA8gW0QJMP4xXCuxRloCR2OUV8MxJLByqEJI9lyKPbcPW9ErcsY23yUqnx-sw8vXvn0xOxLoUCr8hjY5HjeOATaXZ_mn-wIqymegiXimSg88eO6IwJPkuG6V-9MzZ3Qpp23OzV4Tj8Ux_LAMdyxYPxdXgkrE2OY5kwqrGj5uxlvFmhQcB-_N0t7W4a5cSZAoHe3NmOxlH-sAjqbp0dtrC3MZlpsoxfRe-TggOnF_9jfw_fE_-ET3tDAlVtNv06LETwVzIf7ABSy3NwcjMP92OtTCWz_by-i1jRHNv2fz75jwkmzNkK5zzM2XQhpIlPhXN75bLlAz_QfdFWWmz8OXzQPynxMirPAaFhOE0cFxvjix9ZT8j_D49_VviCpPvU2E5t6sTOf4Itu7xcpYD_WLLY3kO2i8aqcvRcvlqiAFSaaUPGasHBeJQicXT5YrvSrxdx8b8gT-JgOBRkLhfVvLFE68G_HVGNVvzBUVjmbwokONzALoFpE8qmkZUgi02KHD89by5Octo0-5xp0CTLjOi56_dTShM0APQ7D0kiolIEK2ZKrJfIDwEc4_FCz551TAs0D8DrxztO4Gtok7sfyPHAfbJ0VvMyfZ0e0Y7Y24-UO2Y3myKLMqyPf2Jf-f",null,null,null,null,null,1]`
	reqBody := bytes.NewBufferString(data)

	// 创建请求
	req, err := http.NewRequest("POST", apiURL, reqBody)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return
	}

	// 设置 Headers
	req.Header.Set("accept", "*/*")
	req.Header.Set("accept-language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("authorization", "SAPISIDHASH 1757409160_0bf21510e9df8acd7a4cd921d062de83f2a7b06e SAPISID1PHASH 1757409160_0bf21510e9df8acd7a4cd921d062de83f2a7b06e SAPISID3PHASH 1757409160_0bf21510e9df8acd7a4cd921d062de83f2a7b06e")
	req.Header.Set("content-type", "application/json+protobuf")
	req.Header.Set("origin", "https://aistudio.google.com")
	req.Header.Set("priority", "u=1, i")
	req.Header.Set("referer", "https://aistudio.google.com/")
	req.Header.Set("sec-ch-ua", `"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"`)
	req.Header.Set("sec-ch-ua-arch", `"arm"`)
	req.Header.Set("sec-ch-ua-bitness", `"64"`)
	req.Header.Set("sec-ch-ua-form-factors", `"Desktop"`)
	req.Header.Set("sec-ch-ua-full-version", `"140.0.7339.81"`)
	req.Header.Set("sec-ch-ua-full-version-list", `"Chromium";v="140.0.7339.81", "Not=A?Brand";v="24.0.0.0", "Google Chrome";v="140.0.7339.81"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-model", `""`)
	req.Header.Set("sec-ch-ua-platform", `"macOS"`)
	req.Header.Set("sec-ch-ua-platform-version", `"15.6.1"`)
	req.Header.Set("sec-ch-ua-wow64", "?0")
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-site", "same-site")
	req.Header.Set("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36")
	req.Header.Set("x-browser-channel", "stable")
	req.Header.Set("x-browser-copyright", "Copyright 2025 Google LLC. All rights reserved.")
	req.Header.Set("x-browser-validation", "jFliu1AvGMEE7cpr93SSytkZ8D4=")
	req.Header.Set("x-browser-year", "2025")
	req.Header.Set("x-client-data", "CIW2yQEIprbJAQiKksoBCKmdygEI5YXLAQiVocsBCIagzQEI/aXOAQiUgc8BCMSDzwEIhYTPAQi3hc8BCPWFzwEI4obPAQ==")
	req.Header.Set("x-goog-api-key", "AIzaSyDdP816MREB3SkjZO04QXbjsigfcI0GWOs")
	req.Header.Set("x-goog-authuser", "0")
	req.Header.Set("x-goog-ext-519733851-bin", "CAASAUIwATgEQAA=")
	req.Header.Set("x-user-agent", "grpc-web-javascript/0.1")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return
	}

	// 输出响应
	fmt.Println("Response Status:", resp.Status)
	fmt.Println("Response Body:", string(body))
}
