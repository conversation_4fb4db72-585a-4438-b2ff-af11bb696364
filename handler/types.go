package handler

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type CommonResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

func SuccessResponse(ctx *gin.Context, data any) {
	ctx.JSON(200, CommonResponse{Code: 1, Msg: "success", Data: data})
}

func ErrorResponse(ctx *gin.Context, code int, msg string) {
	ctx.JSON(200, CommonResponse{Code: 0, Msg: msg, Data: nil})
}

// UserMatchStats 用户匹配统计
type UserMatchStats struct {
	UserID      string `json:"user_id"`       // 用户ID
	MatchCount  int    `json:"match_count"`   // 匹配次数
	LastMatchAt string `json:"last_match_at"` // 最后匹配时间
}

// ChatHistory 聊天历史
type ChatHistory struct {
	RoomID   string    `json:"room_id"`  // 房间ID
	Messages []Message `json:"messages"` // 消息列表
	Users    []string  `json:"users"`    // 参与用户列表
	StartAt  time.Time `json:"start_at"` // 聊天开始时间
	EndAt    time.Time `json:"end_at"`   // 聊天结束时间
}

// GenerateMessageID 生成唯一消息ID
func GenerateMessageID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// GenerateAIUserID 生成AI用户ID
func GenerateAIUserID() string {
	bytes := make([]byte, 6)
	rand.Read(bytes)
	return "ai_" + hex.EncodeToString(bytes)
}

// IsAIUser 检查是否为AI用户
func IsAIUser(userID string) bool {
	return len(userID) > 3 && userID[:3] == "ai_"
}

// UserState 用户状态
type UserState string

const (
	StateIdle     UserState = "idle"
	StateMatching UserState = "matching"
	StateChatting UserState = "chatting"
)

// UserType 用户类型
type UserType string

const (
	UserTypeHuman UserType = "human" // 真实用户
	UserTypeAI    UserType = "ai"    // AI用户
)

// User 用户信息
type User struct {
	ID    string
	Type  UserType // 用户类型
	State UserState
	Conn  *websocket.Conn // WS连接（聊天时使用）
}

// MatchRequest 匹配请求
type MatchRequest struct {
	UserID string `json:"user_id"`
}

// MatchResponse 匹配响应
type MatchResponse struct {
	Matched bool   `json:"matched"`
	RoomID  string `json:"room_id"`
	Partner string `json:"partner_id"` // 可选，返回对方ID
}

// Room 聊天室
type Room struct {
	ID      string
	Users   map[string]*User
	MsgChan chan Message
}

// Message 消息结构体
type Message struct {
	ID        string    `json:"id,omitempty"`        // 消息唯一ID
	From      string    `json:"from"`                // 发送者用户ID
	Content   string    `json:"content"`             // 消息内容
	Type      string    `json:"type"`                // text/image/audio/video
	Timestamp time.Time `json:"timestamp,omitempty"` // 消息时间戳
	RoomID    string    `json:"room_id,omitempty"`   // 聊天室ID
}

type GithubUserInfo struct {
	AvatarURL         string    `json:"avatar_url"`
	Bio               string    `json:"bio"`
	Company           string    `json:"company"`
	CreatedAt         time.Time `json:"created_at"`
	Email             *string   `json:"email"`
	EventsURL         string    `json:"events_url"`
	Followers         int       `json:"followers"`
	FollowersURL      string    `json:"followers_url"`
	Following         int       `json:"following"`
	FollowingURL      string    `json:"following_url"`
	GistsURL          string    `json:"gists_url"`
	GravatarID        string    `json:"gravatar_id"`
	Hireable          *bool     `json:"hireable"`
	HTMLURL           string    `json:"html_url"`
	ID                int       `json:"id"`
	Location          *string   `json:"location"`
	Login             string    `json:"login"`
	Name              string    `json:"name"`
	NodeID            string    `json:"node_id"`
	OrganizationsURL  string    `json:"organizations_url"`
	PublicGists       int       `json:"public_gists"`
	PublicRepos       int       `json:"public_repos"`
	ReceivedEventsURL string    `json:"received_events_url"`
	ReposURL          string    `json:"repos_url"`
	SiteAdmin         bool      `json:"site_admin"`
	StarredURL        string    `json:"starred_url"`
	SubscriptionsURL  string    `json:"subscriptions_url"`
	TwitterUsername   *string   `json:"twitter_username"`
	Type              string    `json:"type"`
	UpdatedAt         time.Time `json:"updated_at"`
	URL               string    `json:"url"`
}

type GoogleUserInfo struct {
	Id            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
}
