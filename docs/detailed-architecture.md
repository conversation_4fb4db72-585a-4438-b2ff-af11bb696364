# Chat Matcher 详细系统架构

```mermaid
graph TB
    subgraph "客户端层"
        A[Web 浏览器<br/>HTML5 + CSS3 + JS] -->|REST API & WebSocket| B
        C[命令行终端<br/>Bubble Tea TUI] -->|REST API & WebSocket| B
    end
    
    subgraph "API网关层"
        B[HTTP服务器<br/>Gin框架]
        B --> D1[中间件<br/>CORS]
    end
    
    subgraph "业务逻辑层"
        D[路由处理器<br/>server.go]
        E[匹配系统<br/>matcher.go]
        F[房间管理<br/>room.go]
        G[WebSocket处理<br/>server.go]
        H[存储接口<br/>storage.go]
        I[AI助手<br/>ai_assistant.go]
        J[数据模型<br/>types.go]
    end
    
    subgraph "数据访问层"
        K[Redis存储<br/>RedisStorage]
        L[Redis管理<br/>redis.go]
    end
    
    subgraph "数据存储层"
        M[Redis数据库]
    end
    
    subgraph "AI服务层"
        N[LangChainGo]
        O[OpenAI API]
    end
    
    subgraph "日志系统"
        P[日志记录<br/>main.go]
    end
    
    %% 连接关系
    B --> D
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    
    E --> J
    E --> K
    E --> M
    
    F --> J
    F --> K
    F --> M
    
    G --> J
    G --> K
    G --> M
    
    H --> K
    K --> L
    L --> M
    
    I --> N
    N --> O
    
    D --> P
    E --> P
    F --> P
    G --> P
    H --> P
    I --> P
    
    %% 样式定义
    style A fill:#4285F4,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#34A853,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#FBBC05,stroke:#333,stroke-width:2px,color:#000
    style M fill:#EA4335,stroke:#333,stroke-width:2px,color:#fff
    style O fill:#4285F4,stroke:#333,stroke-width:2px,color:#fff
    
    classDef component fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:2px;
    classDef data fill:#fbb,stroke:#333,stroke-width:2px;
    classDef external fill:#bfb,stroke:#333,stroke-width:2px;
```

## 组件详细说明

### 客户端层
- **Web 浏览器**: 基于HTML5、CSS3和原生JavaScript构建的现代化Web界面
  - 支持深色/浅色主题切换
  - 响应式设计，适配移动端
  - 支持文本和图片消息
  - 实时WebSocket通信

- **命令行终端**: 基于Bubble Tea TUI框架构建的终端用户界面
  - 提供菜单导航系统
  - 支持匹配状态显示
  - 实时聊天界面

### API网关层
- **HTTP服务器**: 基于Gin框架构建的高性能HTTP服务器
  - 提供RESTful API接口
  - 处理静态文件服务
  - 支持CORS跨域请求

### 业务逻辑层

#### 1. 路由处理器 (server.go)
- 处理所有HTTP请求路由
- 协调各业务组件工作
- 管理WebSocket连接升级

#### 2. 匹配系统 (matcher.go)
- 实现用户随机匹配算法
- 管理用户状态（空闲、匹配中、聊天中）
- 支持AI用户匹配
- 维护等待队列

#### 3. 房间管理 (room.go)
- 管理聊天房间生命周期
- 处理消息广播机制
- 管理WebSocket连接
- 支持AI房间特殊处理

#### 4. WebSocket处理 (server.go)
- 处理WebSocket连接建立
- 管理消息收发
- 处理连接断开

#### 5. 存储接口 (storage.go)
- 定义统一的数据存储接口
- 实现Redis存储方案
- 管理聊天记录、用户统计等

#### 6. AI助手 (ai_assistant.go)
- 集成LangChainGo框架
- 连接OpenAI API
- 提供AI聊天功能
- 支持上下文对话

#### 7. 数据模型 (types.go)
- 定义系统核心数据结构
- 包括用户、消息、房间等模型
- 提供工具函数

### 数据访问层
- **Redis存储**: 实现Storage接口的Redis存储方案
- **Redis管理**: 管理Redis连接和配置

### 数据存储层
- **Redis数据库**: 存储用户状态、聊天记录、匹配统计等持久化数据

### AI服务层
- **LangChainGo**: Go语言实现的LangChain框架
- **OpenAI API**: OpenAI提供的AI服务接口

### 日志系统
- **日志记录**: 统一的日志记录和管理机制

## 核心数据流

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API网关
    participant Matcher as 匹配系统
    participant Room as 房间管理
    participant Storage as 存储系统
    participant AI as AI助手
    participant Redis as Redis数据库
    
    Client->>API: 发起匹配请求
    API->>Matcher: 处理匹配逻辑
    Matcher->>Storage: 更新用户状态
    Storage->>Redis: 保存状态数据
    Matcher-->>API: 返回匹配结果
    API-->>Client: 返回匹配成功
    
    Client->>API: 建立WebSocket连接
    API->>Room: 创建房间并加入
    Room->>Storage: 创建房间记录
    Storage->>Redis: 保存房间信息
    
    Client->>API: 发送消息
    API->>Room: 广播消息
    Room->>Storage: 保存消息记录
    Storage->>Redis: 存储消息
    
    Room->>AI: 检测是否需要AI回复
    AI->>AI: 生成AI回复
    AI-->>Room: 返回AI消息
    Room->>Client: 广播AI回复
```

## 系统特性

### 核心功能
1. **智能匹配**: 基于队列的随机匹配算法
2. **实时通信**: WebSocket双向通信，低延迟消息传递
3. **多端支持**: 支持Web浏览器和命令行终端
4. **房间管理**: 自动创建和管理聊天房间
5. **状态管理**: 完整的用户状态跟踪和管理

### 技术特性
1. **高并发**: Goroutines + Mutex实现并发安全
2. **可扩展**: 模块化设计，易于扩展新功能
3. **容器化**: 支持Docker部署
4. **跨平台**: 支持Linux和macOS的x86和ARM平台
5. **AI集成**: 集成OpenAI API提供智能聊天

### 部署特性
1. **多平台构建**: 支持多种操作系统和架构
2. **Docker支持**: 提供Dockerfile和docker-compose配置
3. **Makefile**: 简化构建和部署流程
4. **日志管理**: 完善的日志记录机制