# Chat Matcher 组件架构与交互关系

```mermaid
graph TD
    subgraph "客户端层"
        A[Web客户端<br/>HTML/JS] -->|HTTP请求| B
        C[CLI客户端<br/>Bubble Tea] -->|HTTP请求| B
    end
    
    subgraph "服务端核心"
        B[主服务<br/>main.go]
        D[路由处理器<br/>server.go]
        E[匹配器<br/>matcher.go]
        F[房间管理器<br/>room.go]
        G[AI助手<br/>ai_assistant.go]
        H[存储接口<br/>storage.go]
        I[Redis存储<br/>redis.go]
        J[数据模型<br/>types.go]
        K[中间件<br/>cors.go]
    end
    
    subgraph "外部依赖"
        L[Redis数据库]
        M[OpenAI API]
    end
    
    %% 主要连接关系
    B --> D
    B --> K
    B --> I
    
    D --> E
    D --> F
    D --> G
    D --> H
    
    E --> J
    E --> I
    
    F --> J
    F --> I
    F --> G
    
    G --> M
    
    H --> I
    
    I --> L
    
    %% 样式
    style A fill:#4285F4,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#34A853,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#FBBC05,stroke:#333,stroke-width:2px,color:#000
    style L fill:#EA4335,stroke:#333,stroke-width:2px,color:#fff
    style M fill:#4285F4,stroke:#333,stroke-width:2px,color:#fff
    
    classDef core fill:#f9f,stroke:#333,stroke-width:2px;
    classDef external fill:#bfb,stroke:#333,stroke-width:2px;
```

## 组件详细交互关系

### 1. 主服务 (main.go)
```mermaid
graph LR
    A[初始化日志] --> B[初始化Redis]
    B --> C[创建存储实例]
    C --> D[初始化处理器]
    D --> E[配置路由]
    E --> F[启动HTTP服务]
```

### 2. 路由处理器交互 (server.go)
```mermaid
graph LR
    A[HTTP请求] --> B{路由匹配}
    B -->|/match| C[匹配处理]
    B -->|/ws| D[WebSocket处理]
    B -->|/chat/history| E[聊天历史]
    B -->|/user/stats| F[用户统计]
    B -->|/user/rooms| G[用户房间]
    
    C --> H[Matcher]
    D --> I[RoomManager]
    E --> J[Storage]
    F --> J
    G --> J
```

### 3. 匹配系统交互 (matcher.go)
```mermaid
graph LR
    A[用户请求匹配] --> B{检查用户状态}
    B -->|已在聊天| C[返回当前房间]
    B -->|未在聊天| D[加入等待队列]
    D --> E{队列是否有用户}
    E -->|有| F[随机匹配]
    E -->|无| G[等待匹配]
    F --> H[更新用户状态]
    F --> I[创建房间]
    H --> J[Storage]
```

### 4. 房间管理交互 (room.go)
```mermaid
graph LR
    A[创建房间] --> B[初始化房间]
    B --> C[启动消息循环]
    C --> D{消息类型}
    D -->|普通消息| E[广播给用户]
    D -->|用户消息| F[生成AI回复]
    F --> G[AIClient]
    G --> H[发送AI回复]
```

### 5. AI助手交互 (ai_assistant.go)
```mermaid
graph LR
    A[用户消息] --> B{消息类型}
    B -->|文本| C[文本处理]
    B -->|图片| D[图片处理]
    C --> E[调用LLM]
    D --> E
    E --> F[OpenAI API]
    F --> G[返回AI回复]
```

## 数据模型关系图

```mermaid
erDiagram
    USER ||--o{ ROOM : "参与"
    USER ||--o{ MESSAGE : "发送"
    ROOM ||--o{ MESSAGE : "包含"
    USER ||--o{ USER_STATS : "统计"
    
    USER {
        string ID
        UserType Type
        UserState State
        websocket.Conn Conn
    }
    
    ROOM {
        string ID
        map Users
        chan MsgChan
    }
    
    MESSAGE {
        string ID
        string From
        string Content
        string Type
        time Timestamp
        string RoomID
    }
    
    USER_STATS {
        string UserID
        int MatchCount
        string LastMatchAt
    }
```

## 系统启动流程

```mermaid
graph TD
    A[启动main.go] --> B[配置日志系统]
    B --> C[初始化Redis连接]
    C --> D[创建Redis存储实例]
    D --> E[初始化处理器]
    E --> F[配置Gin路由]
    F --> G[添加CORS中间件]
    G --> H[注册API路由]
    H --> I[启动HTTP服务器]
    I --> J[监听端口9093]
```

## 用户匹配流程

```mermaid
graph TD
    A[用户发起匹配请求] --> B[MatchHandle处理]
    B --> C[调用match函数]
    C --> D{用户是否已在聊天}
    D -->|是| E[返回当前房间信息]
    D -->|否| F[调用RequestMatch]
    F --> G{等待队列是否为空}
    G -->|是| H[加入等待队列]
    G -->|否| I[随机选择匹配用户]
    I --> J[更新双方状态为聊天中]
    J --> K[创建房间]
    K --> L[返回匹配成功]
    H --> M[等待1秒后重试]
    M --> N{是否超时}
    N -->|否| O[继续等待]
    N -->|是| P[尝试AI匹配]
    P --> Q{AI匹配是否成功}
    Q -->|是| R[创建AI房间]
    Q -->|否| S[返回匹配失败]
```

## WebSocket通信流程

```mermaid
graph TD
    A[用户连接WebSocket] --> B[WSHandle处理]
    B --> C[升级HTTP连接为WebSocket]
    C --> D[RoomManager.JoinRoom]
    D --> E[用户加入房间]
    E --> F[启动消息处理协程]
    F --> G{接收消息}
    G -->|文本消息| H[广播给房间其他用户]
    G -->|图片消息| I[广播并生成AI回复]
    H --> J[保存消息到存储]
    I --> J
    I --> K[调用AI助手]
    K --> L[发送AI回复]
```

## 数据存储结构

```mermaid
graph TD
    A[Redis存储结构] --> B[聊天历史<br/>chat:room:{roomID}]
    A --> C[用户房间<br/>user:rooms:{userID}]
    A --> D[匹配统计<br/>user:stats:{userID}]
    A --> E[房间信息<br/>room:info:{roomID}]
    
    B --> F[消息列表 LPUSH/LRANGE]
    C --> G[房间集合 SADD/SMEMBERS]
    D --> H[哈希表 HINCRBY/HGETALL]
    E --> I[哈希表 HMSET/HGETALL]
```

这个详细的组件架构图展示了系统的各个组件之间的关系和交互方式，以及核心业务流程和数据存储结构。