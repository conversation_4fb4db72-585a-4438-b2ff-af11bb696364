# Chat Matcher 系统架构

```mermaid
graph TB
    subgraph "客户端层"
        A[Web 浏览器] -->|HTTP/WebSocket| B
        C[命令行终端] -->|HTTP/WebSocket| B
    end
    
    subgraph "服务端"
        B[HTTP 服务器<br/>Gin框架] --> D[路由处理器]
        D --> E[匹配处理器<br/>Matcher]
        D --> F[房间管理器<br/>RoomManager]
        D --> G[WebSocket处理器]
        D --> H[存储接口<br/>Storage]
        D --> I[AI助手<br/>AIClient]
        
        E --> J[用户状态管理]
        E --> K[匹配队列]
        E --> L[AI匹配]
        
        F --> M[房间管理]
        F --> N[消息广播]
        F --> O[WebSocket连接管理]
        
        G --> P[WebSocket连接]
        G --> Q[消息处理]
        
        H --> R[Redis存储实现]
        
        I --> S[Lang<PERSON>hainGo]
        I --> T[OpenAI API]
    end
    
    subgraph "数据存储层"
        R --> U[Redis数据库]
    end
    
    subgraph "第三方服务"
        T --> V[OpenAI]
    end
    
    style A fill:#4285F4,stroke:#333,stroke-width:2px
    style C fill:#34A853,stroke:#333,stroke-width:2px
    style B fill:#FBBC05,stroke:#333,stroke-width:2px
    style R fill:#EA4335,stroke:#333,stroke-width:2px
    style U fill:#EA4335,stroke:#333,stroke-width:2px
    style T fill:#4285F4,stroke:#333,stroke-width:2px
    style V fill:#4285F4,stroke:#333,stroke-width:2px
    
    classDef component fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:2px;
    classDef data fill:#fbb,stroke:#333,stroke-width:2px;
    classDef external fill:#bfb,stroke:#333,stroke-width:2px;
```

## 组件说明

### 客户端层
- **Web 浏览器**: 提供现代化的Web聊天界面，支持文本和图片消息
- **命令行终端**: 提供TUI（终端用户界面）聊天客户端

### 服务端核心组件

#### 1. HTTP 服务器 (main.go)
- 基于Gin框架构建
- 提供RESTful API接口
- 处理静态文件服务

#### 2. 路由处理器 (handler/server.go)
- 处理所有HTTP请求
- 管理WebSocket连接
- 协调各业务组件

#### 3. 匹配处理器 (handler/matcher.go)
- 实现用户匹配算法
- 管理用户状态（空闲、匹配中、聊天中）
- 支持AI用户匹配

#### 4. 房间管理器 (handler/room.go)
- 管理聊天房间生命周期
- 处理消息广播
- 管理WebSocket连接

#### 5. WebSocket处理器 (handler/server.go)
- 处理实时消息传输
- 管理连接状态

#### 6. 存储接口 (handler/storage.go)
- 定义数据存储接口
- 实现Redis存储

#### 7. AI助手 (handler/ai_assistant.go)
- 集成OpenAI API
- 提供AI聊天功能
- 支持上下文对话

### 数据存储层
- **Redis数据库**: 存储用户状态、聊天记录、匹配统计等

### 第三方服务
- **OpenAI API**: 提供AI聊天能力

## 数据流向

1. 客户端通过HTTP请求发起匹配
2. 匹配处理器处理匹配逻辑
3. 匹配成功后创建聊天房间
4. 客户端通过WebSocket连接到房间
5. 消息通过WebSocket实时传输
6. 聊天记录存储到Redis
7. AI助手提供智能回复功能